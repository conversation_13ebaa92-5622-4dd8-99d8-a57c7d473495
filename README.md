# 社区服务平台

一个基于微服务架构的社区服务平台，支持代取快递、代丢垃圾等多种社区服务。

## 项目结构

```
communityApp/
├── communityApi/          # 后端API服务 (Spring Boot)
├── communityUi/           # 用户端前端 (UniApp)
├── communityServiceUi/    # 服务人员端前端 (UniApp)
├── communityBackUi/       # 管理后台前端 (Vue3)
├── database/              # 数据库脚本
├── docs/                  # 项目文档
└── README.md
```

## 技术栈

### 后端 (communityApi)
- **框架**: Spring Boot 3.2.0
- **数据库**: MySQL 8.0
- **ORM**: MyBatis-Plus 3.5.4
- **缓存**: Redis
- **安全**: Spring Security + JWT
- **连接池**: Druid
- **构建工具**: Maven

### 用户端前端 (communityUi)
- **框架**: UniApp (Vue3 + TypeScript)
- **状态管理**: Pinia
- **HTTP客户端**: Axios
- **支持平台**: 微信小程序、H5、App

### 服务人员端前端 (communityServiceUi)
- **框架**: UniApp (Vue3 + TypeScript)
- **状态管理**: Pinia
- **HTTP客户端**: Axios
- **支持平台**: 微信小程序、H5、App

### 管理后台前端 (communityBackUi)
- **框架**: Vue3 + TypeScript
- **UI组件**: Element Plus
- **状态管理**: Vuex
- **路由**: Vue Router
- **构建工具**: Vue CLI

## 功能特性

### 用户端功能
- 用户注册/登录
- 浏览服务类型
- 发布服务需求
- 查看订单状态
- 评价服务

### 服务人员端功能
- 服务人员注册/登录
- 查看待接单列表
- 接单/完成订单
- 收益统计
- 个人信息管理

### 管理后台功能
- 用户管理
- 服务人员管理
- 订单管理
- 服务类型管理
- 数据统计分析

## 快速开始

### 环境要求
- Java 17+
- Node.js 16+
- MySQL 8.0+
- Redis 6.0+

### 数据库初始化

1. 创建数据库
```sql
CREATE DATABASE community_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. 执行初始化脚本
```bash
mysql -u root -p community_db < database/init.sql
```

### 后端启动

1. 进入后端目录
```bash
cd communityApi
```

2. 修改配置文件
编辑 `src/main/resources/application.yml`，配置数据库连接信息

3. 启动应用
```bash
mvn spring-boot:run
```

后端服务将在 http://localhost:8080 启动

### 前端启动

#### 用户端 (communityUi)
```bash
cd communityUi
npm install
npm run dev:h5
```

#### 服务人员端 (communityServiceUi)
```bash
cd communityServiceUi
npm install
npm run dev:h5
```

#### 管理后台 (communityBackUi)
```bash
cd communityBackUi
npm install
npm run serve
```

管理后台将在 http://localhost:8081 启动

## 默认账户

### 管理员账户
- 用户名: `admin`
- 密码: `admin123`

### 测试用户账户
- 用户名: `user001`
- 密码: `123456`

### 测试服务人员账户
- 用户名: `provider001`
- 密码: `123456`

## API文档

详细的API接口文档请查看: [API接口文档](docs/API接口文档.md)

## 项目特点

1. **多端支持**: UniApp实现一套代码多端运行
2. **前后端分离**: 清晰的架构设计，便于维护和扩展
3. **权限管理**: 基于JWT的认证授权机制
4. **数据安全**: 密码加密存储，SQL注入防护
5. **响应式设计**: 适配不同屏幕尺寸
6. **实时通信**: 支持订单状态实时更新

## 开发规范

### 代码规范
- 后端遵循阿里巴巴Java开发手册
- 前端遵循Vue官方风格指南
- 统一使用UTF-8编码
- 统一使用LF换行符

### 提交规范
- feat: 新功能
- fix: 修复bug
- docs: 文档更新
- style: 代码格式调整
- refactor: 代码重构
- test: 测试相关
- chore: 构建过程或辅助工具的变动

## 部署说明

### 生产环境部署

1. **后端部署**
```bash
cd communityApi
mvn clean package
java -jar target/community-api-1.0.0.jar
```

2. **前端部署**
```bash
# 用户端
cd communityUi
npm run build:h5

# 服务人员端
cd communityServiceUi
npm run build:h5

# 管理后台
cd communityBackUi
npm run build
```

### Docker部署
TODO: 添加Docker配置文件

## 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系方式

如有问题或建议，请通过以下方式联系：

- 邮箱: <EMAIL>
- 项目地址: https://github.com/your-username/communityApp

## 更新日志

### v1.0.0 (2024-01-15)
- 初始版本发布
- 实现基础的用户、服务人员、管理员功能
- 支持代取快递、代丢垃圾等基础服务类型
