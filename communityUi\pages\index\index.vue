<template>
  <view class="container">
    <!-- 轮播图 -->
    <swiper class="banner" indicator-dots autoplay interval="3000" duration="500">
      <swiper-item v-for="(item, index) in banners" :key="index">
        <image :src="item.image" mode="aspectFill" class="banner-image"></image>
      </swiper-item>
    </swiper>

    <!-- 服务分类 -->
    <view class="service-grid">
      <view class="service-item" v-for="(item, index) in services" :key="index" @click="goToService(item)">
        <image :src="item.icon" class="service-icon"></image>
        <text class="service-name">{{ item.name }}</text>
      </view>
    </view>

    <!-- 最新订单 -->
    <view class="section">
      <view class="section-title">最新订单</view>
      <view class="order-list">
        <view class="order-item" v-for="(item, index) in recentOrders" :key="index">
          <view class="order-info">
            <text class="order-title">{{ item.title }}</text>
            <text class="order-time">{{ item.createTime }}</text>
          </view>
          <view class="order-status">{{ item.status }}</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

// 轮播图数据
const banners = ref([
  { image: '/static/banner1.jpg' },
  { image: '/static/banner2.jpg' },
  { image: '/static/banner3.jpg' }
])

// 服务分类数据
const services = ref([
  { id: 1, name: '代取快递', icon: '/static/service-express.png' },
  { id: 2, name: '代丢垃圾', icon: '/static/service-trash.png' },
  { id: 3, name: '代买商品', icon: '/static/service-shopping.png' },
  { id: 4, name: '其他服务', icon: '/static/service-other.png' }
])

// 最新订单数据
const recentOrders = ref([
  { id: 1, title: '代取快递', createTime: '2024-01-15 10:30', status: '进行中' },
  { id: 2, title: '代丢垃圾', createTime: '2024-01-14 16:20', status: '已完成' }
])

// 跳转到服务页面
const goToService = (service: any) => {
  uni.navigateTo({
    url: `/pages/service/service?type=${service.id}`
  })
}

onMounted(() => {
  // 页面加载时的逻辑
  console.log('首页加载完成')
})
</script>

<style scoped>
.container {
  padding: 0;
}

.banner {
  width: 100%;
  height: 400rpx;
}

.banner-image {
  width: 100%;
  height: 100%;
}

.service-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
  padding: 40rpx 20rpx;
  background: #fff;
  margin-top: 20rpx;
}

.service-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
}

.service-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 10rpx;
}

.service-name {
  font-size: 24rpx;
  color: #333;
}

.section {
  background: #fff;
  margin-top: 20rpx;
  padding: 30rpx 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.order-list {
  /* 订单列表样式 */
}

.order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.order-info {
  flex: 1;
}

.order-title {
  font-size: 28rpx;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.order-time {
  font-size: 24rpx;
  color: #999;
}

.order-status {
  font-size: 24rpx;
  color: #3cc51f;
}
</style>
