package com.community.controller;

import com.community.common.Result;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 测试控制器
 */
@RestController
@RequestMapping("/public/test")
public class TestController {

    @GetMapping("/hello")
    public Result<String> hello() {
        return Result.success("Hello, Community API!");
    }

    @GetMapping("/health")
    public Result<String> health() {
        return Result.success("API服务运行正常");
    }
}
