package com.community.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 服务类型实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("service_types")
public class ServiceType extends BaseEntity {

    /**
     * 服务类型名称
     */
    private String name;

    /**
     * 服务描述
     */
    private String description;

    /**
     * 图标URL
     */
    private String icon;

    /**
     * 基础价格
     */
    private BigDecimal basePrice;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;
}
