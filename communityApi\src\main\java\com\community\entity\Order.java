package com.community.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("orders")
public class Order extends BaseEntity {

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 服务人员ID
     */
    private Long serviceProviderId;

    /**
     * 服务类型ID
     */
    private Long serviceTypeId;

    /**
     * 服务标题
     */
    private String title;

    /**
     * 服务描述
     */
    private String description;

    /**
     * 服务地址
     */
    private String address;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 期望价格
     */
    private BigDecimal expectedPrice;

    /**
     * 实际价格
     */
    private BigDecimal actualPrice;

    /**
     * 订单状态：pending-待接单，accepted-已接单，in_progress-进行中，completed-已完成，cancelled-已取消
     */
    private String status;

    /**
     * 接单时间
     */
    private LocalDateTime acceptTime;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 完成时间
     */
    private LocalDateTime completeTime;

    /**
     * 取消时间
     */
    private LocalDateTime cancelTime;

    /**
     * 取消原因
     */
    private String cancelReason;

    /**
     * 评分：1-5分
     */
    private Integer rating;

    /**
     * 评价内容
     */
    private String comment;
}
