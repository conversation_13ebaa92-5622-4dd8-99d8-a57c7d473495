<template>
  <view class="container">
    <view class="service-form card">
      <view class="form-title">发布服务需求</view>
      
      <view class="form-item">
        <text class="label">服务类型</text>
        <picker @change="onServiceTypeChange" :value="formData.serviceType" :range="serviceTypes" range-key="name">
          <view class="picker">
            {{ serviceTypes[formData.serviceType]?.name || '请选择服务类型' }}
          </view>
        </picker>
      </view>

      <view class="form-item">
        <text class="label">服务标题</text>
        <input v-model="formData.title" placeholder="请输入服务标题" class="input" />
      </view>

      <view class="form-item">
        <text class="label">服务描述</text>
        <textarea v-model="formData.description" placeholder="请详细描述您的服务需求" class="textarea" />
      </view>

      <view class="form-item">
        <text class="label">联系电话</text>
        <input v-model="formData.phone" placeholder="请输入联系电话" class="input" type="number" />
      </view>

      <view class="form-item">
        <text class="label">服务地址</text>
        <input v-model="formData.address" placeholder="请输入详细地址" class="input" />
      </view>

      <view class="form-item">
        <text class="label">期望价格</text>
        <input v-model="formData.price" placeholder="请输入期望价格" class="input" type="digit" />
      </view>

      <button class="btn btn-primary submit-btn" @click="submitOrder">发布需求</button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, onLoad } from '@dcloudio/uni-app'

// 表单数据
const formData = reactive({
  serviceType: 0,
  title: '',
  description: '',
  phone: '',
  address: '',
  price: ''
})

// 服务类型
const serviceTypes = ref([
  { id: 1, name: '代取快递' },
  { id: 2, name: '代丢垃圾' },
  { id: 3, name: '代买商品' },
  { id: 4, name: '其他服务' }
])

// 服务类型选择
const onServiceTypeChange = (e: any) => {
  formData.serviceType = e.detail.value
}

// 提交订单
const submitOrder = () => {
  // 表单验证
  if (!formData.title) {
    uni.showToast({
      title: '请输入服务标题',
      icon: 'none'
    })
    return
  }

  if (!formData.description) {
    uni.showToast({
      title: '请输入服务描述',
      icon: 'none'
    })
    return
  }

  if (!formData.phone) {
    uni.showToast({
      title: '请输入联系电话',
      icon: 'none'
    })
    return
  }

  if (!formData.address) {
    uni.showToast({
      title: '请输入服务地址',
      icon: 'none'
    })
    return
  }

  // 提交数据
  console.log('提交订单数据:', formData)
  
  uni.showToast({
    title: '发布成功',
    icon: 'success'
  })

  // 跳转到订单页面
  setTimeout(() => {
    uni.switchTab({
      url: '/pages/order/order'
    })
  }, 1500)
}

// 页面加载
onLoad((options) => {
  if (options?.type) {
    formData.serviceType = parseInt(options.type) - 1
  }
})
</script>

<style scoped>
.container {
  padding: 20rpx;
}

.service-form {
  margin-top: 0;
}

.form-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 40rpx;
  text-align: center;
}

.form-item {
  margin-bottom: 30rpx;
}

.label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.input, .textarea, .picker {
  width: 100%;
  padding: 20rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  background: #fff;
}

.textarea {
  height: 120rpx;
  resize: none;
}

.picker {
  color: #333;
  display: flex;
  align-items: center;
  min-height: 40rpx;
}

.submit-btn {
  width: 100%;
  margin-top: 40rpx;
  padding: 30rpx;
  font-size: 32rpx;
}
</style>
