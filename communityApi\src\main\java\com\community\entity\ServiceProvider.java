package com.community.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 服务人员实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("service_providers")
public class ServiceProvider extends BaseEntity {

    /**
     * 用户名
     */
    private String username;

    /**
     * 密码
     */
    private String password;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 头像URL
     */
    private String avatar;

    /**
     * 性别：0-未知，1-男，2-女
     */
    private Integer gender;

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 地址
     */
    private String address;

    /**
     * 服务区域
     */
    private String serviceArea;

    /**
     * 服务类型（逗号分隔）
     */
    private String serviceTypes;

    /**
     * 评分
     */
    private BigDecimal rating;

    /**
     * 总订单数
     */
    private Integer totalOrders;

    /**
     * 完成订单数
     */
    private Integer completedOrders;

    /**
     * 总收益
     */
    private BigDecimal totalEarnings;

    /**
     * 状态：0-禁用，1-正常，2-审核中
     */
    private Integer status;
}
