# 社区服务平台 API 接口文档

## 基础信息

- **基础URL**: `http://localhost:8080/api`
- **认证方式**: JWT Token
- **请求格式**: JSON
- **响应格式**: JSON

## 统一响应格式

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {}
}
```

### 状态码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 操作成功 |
| 400 | 参数错误 |
| 401 | 未认证 |
| 403 | 无权限 |
| 404 | 资源不存在 |
| 500 | 服务器错误 |

## 认证接口

### 1. 用户登录

**接口地址**: `POST /auth/user/login`

**请求参数**:
```json
{
  "username": "user001",
  "password": "123456"
}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "userInfo": {
      "id": 1,
      "username": "user001",
      "nickname": "张三",
      "phone": "13800138001",
      "avatar": null
    }
  }
}
```

### 2. 服务人员登录

**接口地址**: `POST /auth/provider/login`

**请求参数**:
```json
{
  "username": "provider001",
  "password": "123456"
}
```

### 3. 管理员登录

**接口地址**: `POST /auth/admin/login`

**请求参数**:
```json
{
  "username": "admin",
  "password": "admin123"
}
```

## 用户端接口

### 1. 获取服务类型列表

**接口地址**: `GET /user/service-types`

**响应数据**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": 1,
      "name": "代取快递",
      "description": "帮助用户代取快递包裹",
      "icon": "/static/service-express.png",
      "basePrice": 5.00
    }
  ]
}
```

### 2. 创建订单

**接口地址**: `POST /user/orders`

**请求参数**:
```json
{
  "serviceTypeId": 1,
  "title": "代取快递",
  "description": "帮忙取一下菜鸟驿站的快递",
  "address": "阳光小区1号楼101室",
  "contactPhone": "13800138001",
  "expectedPrice": 5.00
}
```

### 3. 获取我的订单列表

**接口地址**: `GET /user/orders`

**请求参数**:
- `status`: 订单状态（可选）
- `page`: 页码（默认1）
- `size`: 每页数量（默认10）

## 服务人员端接口

### 1. 获取待接单列表

**接口地址**: `GET /provider/orders/pending`

**请求参数**:
- `page`: 页码（默认1）
- `size`: 每页数量（默认10）

### 2. 接单

**接口地址**: `POST /provider/orders/{orderId}/accept`

### 3. 完成订单

**接口地址**: `POST /provider/orders/{orderId}/complete`

**请求参数**:
```json
{
  "actualPrice": 5.00,
  "remark": "服务完成"
}
```

### 4. 获取我的订单列表

**接口地址**: `GET /provider/orders`

**请求参数**:
- `status`: 订单状态（可选）
- `page`: 页码（默认1）
- `size`: 每页数量（默认10）

## 管理员端接口

### 1. 获取统计数据

**接口地址**: `GET /admin/statistics`

**响应数据**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "totalUsers": 1256,
    "totalServiceProviders": 89,
    "totalOrders": 3421,
    "totalRevenue": 45678.50
  }
}
```

### 2. 用户管理

**获取用户列表**: `GET /admin/users`
**获取用户详情**: `GET /admin/users/{userId}`
**更新用户状态**: `PUT /admin/users/{userId}/status`

### 3. 服务人员管理

**获取服务人员列表**: `GET /admin/service-providers`
**审核服务人员**: `PUT /admin/service-providers/{providerId}/audit`

### 4. 订单管理

**获取订单列表**: `GET /admin/orders`
**获取订单详情**: `GET /admin/orders/{orderId}`

## 错误处理

### 常见错误响应

```json
{
  "code": 401,
  "message": "未认证，请先登录",
  "data": null
}
```

```json
{
  "code": 400,
  "message": "参数验证失败",
  "data": {
    "field": "username",
    "message": "用户名不能为空"
  }
}
```

## 请求头说明

### 认证请求头
```
Authorization: Bearer {token}
```

### 内容类型
```
Content-Type: application/json
```
