-- 社区服务平台数据库初始化脚本

-- 创建数据库
CREATE DATABASE IF NOT EXISTS community_db DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE community_db;

-- 用户表
CREATE TABLE `users` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `nickname` varchar(50) DEFAULT NULL COMMENT '昵称',
  `phone` varchar(20) NOT NULL COMMENT '手机号',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像URL',
  `gender` tinyint DEFAULT '0' COMMENT '性别：0-未知，1-男，2-女',
  `birthday` date DEFAULT NULL COMMENT '生日',
  `address` varchar(255) DEFAULT NULL COMMENT '地址',
  `status` tinyint DEFAULT '1' COMMENT '状态：0-禁用，1-正常',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` bigint DEFAULT NULL COMMENT '创建人ID',
  `update_by` bigint DEFAULT NULL COMMENT '更新人ID',
  `deleted` tinyint DEFAULT '0' COMMENT '逻辑删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  UNIQUE KEY `uk_phone` (`phone`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 服务人员表
CREATE TABLE `service_providers` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '服务人员ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `real_name` varchar(50) NOT NULL COMMENT '真实姓名',
  `phone` varchar(20) NOT NULL COMMENT '手机号',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像URL',
  `gender` tinyint DEFAULT '0' COMMENT '性别：0-未知，1-男，2-女',
  `id_card` varchar(18) NOT NULL COMMENT '身份证号',
  `address` varchar(255) DEFAULT NULL COMMENT '地址',
  `service_area` varchar(255) DEFAULT NULL COMMENT '服务区域',
  `service_types` varchar(255) DEFAULT NULL COMMENT '服务类型（逗号分隔）',
  `rating` decimal(3,2) DEFAULT '5.00' COMMENT '评分',
  `total_orders` int DEFAULT '0' COMMENT '总订单数',
  `completed_orders` int DEFAULT '0' COMMENT '完成订单数',
  `total_earnings` decimal(10,2) DEFAULT '0.00' COMMENT '总收益',
  `status` tinyint DEFAULT '1' COMMENT '状态：0-禁用，1-正常，2-审核中',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` bigint DEFAULT NULL COMMENT '创建人ID',
  `update_by` bigint DEFAULT NULL COMMENT '更新人ID',
  `deleted` tinyint DEFAULT '0' COMMENT '逻辑删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  UNIQUE KEY `uk_phone` (`phone`),
  UNIQUE KEY `uk_id_card` (`id_card`),
  KEY `idx_status` (`status`),
  KEY `idx_rating` (`rating`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='服务人员表';

-- 服务类型表
CREATE TABLE `service_types` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '服务类型ID',
  `name` varchar(50) NOT NULL COMMENT '服务类型名称',
  `description` varchar(255) DEFAULT NULL COMMENT '服务描述',
  `icon` varchar(255) DEFAULT NULL COMMENT '图标URL',
  `base_price` decimal(10,2) DEFAULT '0.00' COMMENT '基础价格',
  `sort_order` int DEFAULT '0' COMMENT '排序',
  `status` tinyint DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` bigint DEFAULT NULL COMMENT '创建人ID',
  `update_by` bigint DEFAULT NULL COMMENT '更新人ID',
  `deleted` tinyint DEFAULT '0' COMMENT '逻辑删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name` (`name`),
  KEY `idx_status` (`status`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='服务类型表';

-- 订单表
CREATE TABLE `orders` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_no` varchar(32) NOT NULL COMMENT '订单号',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `service_provider_id` bigint DEFAULT NULL COMMENT '服务人员ID',
  `service_type_id` bigint NOT NULL COMMENT '服务类型ID',
  `title` varchar(100) NOT NULL COMMENT '服务标题',
  `description` text COMMENT '服务描述',
  `address` varchar(255) NOT NULL COMMENT '服务地址',
  `contact_phone` varchar(20) NOT NULL COMMENT '联系电话',
  `expected_price` decimal(10,2) DEFAULT NULL COMMENT '期望价格',
  `actual_price` decimal(10,2) DEFAULT NULL COMMENT '实际价格',
  `status` varchar(20) DEFAULT 'pending' COMMENT '订单状态：pending-待接单，accepted-已接单，in_progress-进行中，completed-已完成，cancelled-已取消',
  `accept_time` datetime DEFAULT NULL COMMENT '接单时间',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `complete_time` datetime DEFAULT NULL COMMENT '完成时间',
  `cancel_time` datetime DEFAULT NULL COMMENT '取消时间',
  `cancel_reason` varchar(255) DEFAULT NULL COMMENT '取消原因',
  `rating` tinyint DEFAULT NULL COMMENT '评分：1-5分',
  `comment` text COMMENT '评价内容',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` bigint DEFAULT NULL COMMENT '创建人ID',
  `update_by` bigint DEFAULT NULL COMMENT '更新人ID',
  `deleted` tinyint DEFAULT '0' COMMENT '逻辑删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_service_provider_id` (`service_provider_id`),
  KEY `idx_service_type_id` (`service_type_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_orders_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`),
  CONSTRAINT `fk_orders_service_provider_id` FOREIGN KEY (`service_provider_id`) REFERENCES `service_providers` (`id`),
  CONSTRAINT `fk_orders_service_type_id` FOREIGN KEY (`service_type_id`) REFERENCES `service_types` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单表';

-- 管理员表
CREATE TABLE `admins` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '管理员ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `real_name` varchar(50) NOT NULL COMMENT '真实姓名',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像URL',
  `role` varchar(20) DEFAULT 'admin' COMMENT '角色：admin-管理员，super_admin-超级管理员',
  `status` tinyint DEFAULT '1' COMMENT '状态：0-禁用，1-正常',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(50) DEFAULT NULL COMMENT '最后登录IP',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` bigint DEFAULT NULL COMMENT '创建人ID',
  `update_by` bigint DEFAULT NULL COMMENT '更新人ID',
  `deleted` tinyint DEFAULT '0' COMMENT '逻辑删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  KEY `idx_status` (`status`),
  KEY `idx_role` (`role`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员表';

-- 插入初始数据

-- 插入服务类型
INSERT INTO `service_types` (`name`, `description`, `base_price`, `sort_order`, `status`) VALUES
('代取快递', '帮助用户代取快递包裹', 5.00, 1, 1),
('代丢垃圾', '帮助用户清理和丢弃垃圾', 8.00, 2, 1),
('代买商品', '帮助用户购买日用品等商品', 15.00, 3, 1),
('其他服务', '其他个性化服务需求', 10.00, 4, 1);

-- 插入管理员账户（密码：admin123）
INSERT INTO `admins` (`username`, `password`, `real_name`, `phone`, `email`, `role`, `status`) VALUES
('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKyZFzUNjPLap2uHnAf1cAPoUKrC', '系统管理员', '13800138000', '<EMAIL>', 'super_admin', 1);

-- 插入测试用户（密码：123456）
INSERT INTO `users` (`username`, `password`, `nickname`, `phone`, `email`, `address`, `status`) VALUES
('user001', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.', '张三', '13800138001', '<EMAIL>', '阳光小区1号楼101室', 1),
('user002', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.', '李四', '13800138002', '<EMAIL>', '阳光小区2号楼201室', 1),
('user003', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.', '王五', '13800138003', '<EMAIL>', '阳光小区3号楼301室', 1);

-- 插入测试服务人员（密码：123456）
INSERT INTO `service_providers` (`username`, `password`, `real_name`, `phone`, `email`, `id_card`, `address`, `service_area`, `service_types`, `status`) VALUES
('provider001', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.', '赵六', '13800138101', '<EMAIL>', '110101199001011234', '阳光小区附近', '阳光小区', '1,2,3,4', 1),
('provider002', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.', '孙七', '13800138102', '<EMAIL>', '110101199002021234', '阳光小区附近', '阳光小区', '1,2,4', 1),
('provider003', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.', '周八', '13800138103', '<EMAIL>', '110101199003031234', '阳光小区附近', '阳光小区', '1,3,4', 1);

-- 插入测试订单
INSERT INTO `orders` (`order_no`, `user_id`, `service_provider_id`, `service_type_id`, `title`, `description`, `address`, `contact_phone`, `expected_price`, `actual_price`, `status`, `accept_time`, `complete_time`) VALUES
('ORD202401150001', 1, 1, 1, '代取快递', '帮忙取一下菜鸟驿站的快递，比较急用', '阳光小区1号楼101室', '13800138001', 5.00, 5.00, 'completed', '2024-01-15 10:35:00', '2024-01-15 11:00:00'),
('ORD202401150002', 2, 2, 2, '代丢垃圾', '家里垃圾比较多，需要帮忙丢到垃圾站', '阳光小区2号楼201室', '13800138002', 8.00, 8.00, 'completed', '2024-01-15 09:20:00', '2024-01-15 09:45:00'),
('ORD202401150003', 3, 1, 3, '代买商品', '帮忙买一些日用品，清单已准备好', '阳光小区3号楼301室', '13800138003', 15.00, 15.00, 'in_progress', '2024-01-15 08:50:00', NULL),
('ORD202401150004', 1, NULL, 1, '代取快递', '快递到了，但是我不在家', '阳光小区1号楼101室', '13800138001', 5.00, NULL, 'pending', NULL, NULL);
