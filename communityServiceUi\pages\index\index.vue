<template>
  <view class="container">
    <!-- 统计卡片 -->
    <view class="stats-section">
      <view class="stats-card">
        <view class="stats-item">
          <text class="stats-number">{{ todayOrders }}</text>
          <text class="stats-label">今日订单</text>
        </view>
        <view class="stats-item">
          <text class="stats-number">{{ todayEarnings }}</text>
          <text class="stats-label">今日收益</text>
        </view>
        <view class="stats-item">
          <text class="stats-number">{{ totalOrders }}</text>
          <text class="stats-label">总订单数</text>
        </view>
      </view>
    </view>

    <!-- 筛选器 -->
    <view class="filter-section">
      <view class="filter-tabs">
        <view 
          class="filter-tab" 
          :class="{ active: currentFilter === item.value }"
          v-for="item in filterTabs" 
          :key="item.value"
          @click="changeFilter(item.value)"
        >
          {{ item.label }}
        </view>
      </view>
    </view>

    <!-- 订单列表 -->
    <view class="orders-section">
      <view class="order-card" v-for="order in filteredOrders" :key="order.id" @click="goToOrderDetail(order)">
        <view class="order-header">
          <view class="order-title">{{ order.title }}</view>
          <view class="order-price">¥{{ order.price }}</view>
        </view>
        
        <view class="order-content">
          <text class="order-desc">{{ order.description }}</text>
          <view class="order-info">
            <text class="order-address">📍 {{ order.address }}</text>
            <text class="order-time">⏰ {{ order.createTime }}</text>
          </view>
        </view>

        <view class="order-footer">
          <view class="order-status">{{ getStatusText(order.status) }}</view>
          <view class="order-actions" v-if="order.status === 'pending'">
            <button class="btn btn-primary" @click.stop="acceptOrder(order)">接单</button>
          </view>
          <view class="order-actions" v-else-if="order.status === 'accepted'">
            <button class="btn btn-secondary" @click.stop="completeOrder(order)">完成订单</button>
          </view>
        </view>
      </view>

      <view v-if="filteredOrders.length === 0" class="empty-state">
        <text>暂无订单</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

// 统计数据
const todayOrders = ref(5)
const todayEarnings = ref(150)
const totalOrders = ref(128)

// 筛选器
const currentFilter = ref('all')
const filterTabs = ref([
  { label: '全部', value: 'all' },
  { label: '待接单', value: 'pending' },
  { label: '进行中', value: 'accepted' },
  { label: '已完成', value: 'completed' }
])

// 订单数据
const orders = ref([
  {
    id: 1,
    title: '代取快递',
    description: '帮忙取一下菜鸟驿站的快递，比较急用',
    price: 5,
    address: '阳光小区1号楼',
    createTime: '2024-01-15 10:30',
    status: 'pending'
  },
  {
    id: 2,
    title: '代丢垃圾',
    description: '家里垃圾比较多，需要帮忙丢到垃圾站',
    price: 8,
    address: '阳光小区3号楼',
    createTime: '2024-01-15 09:15',
    status: 'pending'
  },
  {
    id: 3,
    title: '代买商品',
    description: '帮忙买一些日用品，清单已准备好',
    price: 15,
    address: '阳光小区2号楼',
    createTime: '2024-01-15 08:45',
    status: 'accepted'
  }
])

// 过滤后的订单
const filteredOrders = computed(() => {
  if (currentFilter.value === 'all') {
    return orders.value
  }
  return orders.value.filter(order => order.status === currentFilter.value)
})

// 切换筛选器
const changeFilter = (filter: string) => {
  currentFilter.value = filter
}

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: '待接单',
    accepted: '进行中',
    completed: '已完成'
  }
  return statusMap[status] || status
}

// 接单
const acceptOrder = (order: any) => {
  uni.showModal({
    title: '确认接单',
    content: `确定要接取"${order.title}"这个订单吗？`,
    success: (res) => {
      if (res.confirm) {
        order.status = 'accepted'
        uni.showToast({
          title: '接单成功',
          icon: 'success'
        })
      }
    }
  })
}

// 完成订单
const completeOrder = (order: any) => {
  uni.showModal({
    title: '确认完成',
    content: `确定已完成"${order.title}"这个订单吗？`,
    success: (res) => {
      if (res.confirm) {
        order.status = 'completed'
        uni.showToast({
          title: '订单已完成',
          icon: 'success'
        })
      }
    }
  })
}

// 跳转到订单详情
const goToOrderDetail = (order: any) => {
  uni.navigateTo({
    url: `/pages/order-detail/order-detail?id=${order.id}`
  })
}

onMounted(() => {
  console.log('接单大厅加载完成')
})
</script>

<style scoped>
.container {
  padding: 0;
}

.stats-section {
  background: linear-gradient(135deg, #007aff, #5ac8fa);
  padding: 40rpx 20rpx;
}

.stats-card {
  display: flex;
  justify-content: space-around;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  padding: 30rpx;
}

.stats-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stats-number {
  font-size: 48rpx;
  font-weight: bold;
  color: #007aff;
  margin-bottom: 10rpx;
}

.stats-label {
  font-size: 24rpx;
  color: #666;
}

.filter-section {
  background: #fff;
  padding: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.filter-tabs {
  display: flex;
  justify-content: space-around;
}

.filter-tab {
  padding: 16rpx 32rpx;
  border-radius: 20rpx;
  font-size: 28rpx;
  color: #666;
  background: #f5f5f5;
}

.filter-tab.active {
  background: #007aff;
  color: #fff;
}

.orders-section {
  padding: 20rpx;
}

.order-card {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.order-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.order-price {
  font-size: 36rpx;
  font-weight: bold;
  color: #ff6b35;
}

.order-content {
  margin-bottom: 20rpx;
}

.order-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  display: block;
  margin-bottom: 15rpx;
}

.order-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.order-address, .order-time {
  font-size: 24rpx;
  color: #999;
}

.order-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-status {
  font-size: 24rpx;
  color: #007aff;
  background: rgba(0, 122, 255, 0.1);
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
}

.order-actions .btn {
  padding: 16rpx 32rpx;
  font-size: 24rpx;
  border-radius: 20rpx;
}

.empty-state {
  text-align: center;
  padding: 100rpx 0;
  color: #999;
  font-size: 28rpx;
}
</style>
